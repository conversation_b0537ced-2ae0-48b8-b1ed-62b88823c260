<!-- sections/tech-navbar.liquid -->
<style>
  /* CSS变量定义 */
  :root {
    --tech-primary: {{ section.settings.primary_color }};
    --tech-secondary: {{ section.settings.secondary_color }};
    --tech-bg-dark: rgba(18, 18, 35, 0.95);
    --tech-bg-darker: rgba(12, 12, 24, 0.98);
    --tech-text: #fff;
    --tech-text-muted: rgba(255, 255, 255, 0.7);
    --tech-border: rgba(255, 255, 255, 0.12);
    --tech-gradient: linear-gradient(135deg, {{ section.settings.primary_color }} 0%, {{ section.settings.secondary_color }} 100%);
    --tech-glass-blur: 15px;
    --tech-shadow-glow: 0 5px 25px rgba({{ section.settings.primary_color | color_to_rgb }}, 0.25);
    --tech-navbar-height: 80px;
    --tech-navbar-mobile-height: 60px;
  }

  /* 导航栏容器 */
  .tech-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: var(--tech-bg-dark);
    backdrop-filter: blur(var(--tech-glass-blur));
    -webkit-backdrop-filter: blur(var(--tech-glass-blur));
    border-bottom: 1px solid var(--tech-border);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    animation: navFadeIn 0.8s ease-out;
  }

  /* 为页面内容添加顶部间距，防止被导航栏遮挡 */
  body.tech-navbar-initialized {
    padding-top: var(--tech-navbar-height);
  }

  @media only screen and (max-width: 768px) {
    body.tech-navbar-initialized {
      padding-top: var(--tech-navbar-mobile-height);
    }
  }

  .tech-navbar.scrolled {
    background: var(--tech-bg-darker);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25), 0 2px 10px var(--tech-shadow-glow);
    border-bottom-color: rgba({{ section.settings.primary_color | color_to_rgb }}, 0.2);
  }

  /* 导航栏内容 */
  .tech-navbar-inner {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--tech-navbar-height);
  }

  /* Logo区域 */
  .tech-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    position: relative;
    z-index: 10;
  }

  .tech-logo-image {
    max-height: 60px;
    width: auto;
    display: block;
  }

  .tech-logo-icon {
    width: 45px;
    height: 45px;
    background: var(--tech-gradient);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 20px rgba({{ section.settings.primary_color | color_to_rgb }}, 0.3);
    animation: float 3s ease-in-out infinite;
  }

  /* 主导航菜单 */
  .tech-nav-menu {
    display: flex;
    align-items: center;
    gap: 40px;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .tech-nav-item {
    position: relative;
  }

  .tech-nav-link {
    color: var(--tech-text-muted);
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .tech-nav-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--tech-gradient);
    transition: width 0.3s ease;
  }

  .tech-nav-link:hover {
    color: var(--tech-text);
    transform: translateY(-2px);
  }

  .tech-nav-link:hover::before {
    width: 100%;
  }

  /* 有下拉菜单的一级菜单链接样式 */
  .tech-nav-link.has-dropdown {
    cursor: pointer;
    pointer-events: auto;
  }

  /* 下拉箭头动画 */
  .tech-nav-link svg {
    transition: transform 0.3s ease, color 0.3s ease;
  }

  .tech-nav-item:hover .tech-nav-link svg {
    transform: rotate(180deg);
    color: var(--tech-primary);
  }

  /* 下拉菜单 */
  .tech-dropdown {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(26, 26, 46, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid var(--tech-border);
    border-radius: 12px;
    padding: 20px;
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-top: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  .tech-nav-item:hover .tech-dropdown {
    opacity: 1;
    visibility: visible;
    margin-top: 10px;
  }

  .tech-dropdown-item {
    display: block;
    padding: 14px 20px;
    color: var(--tech-text-muted);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    margin-bottom: 4px;
    text-align: center;
    font-weight: 500;
  }

  .tech-dropdown-item:hover {
    background: rgba(66, 165, 245, 0.1);
    color: var(--tech-text);
    transform: translateX(5px);
  }

  /* 右侧操作区 */
  .tech-nav-actions {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .tech-action-btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: 1px solid var(--tech-border);
    background: rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--tech-text-muted);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
  }

  .tech-action-btn:hover {
    border-color: var(--tech-primary);
    color: var(--tech-primary);
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba({{ section.settings.primary_color | color_to_rgb }}, 0.3);
  }

  /* CTA按钮 */
  .tech-cta-btn {
    padding: 12px 30px;
    background: var(--tech-gradient);
    border: none;
    border-radius: 60px;
    color: var(--tech-text);
    font-weight: 600;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-block;
  }

  .tech-cta-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .tech-cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba({{ section.settings.primary_color | color_to_rgb }}, 0.4);
  }

  .tech-cta-btn:hover::before {
    width: 300px;
    height: 300px;
  }

  /* 移动端菜单按钮 */
  .tech-mobile-toggle {
    display: none;
    width: 45px;
    height: 45px;
    border: 1px solid var(--tech-border);
    border-radius: 8px;
    background: transparent;
    cursor: pointer;
    position: relative;
  }

  .tech-mobile-toggle span {
    display: block;
    width: 22px;
    height: 2px;
    background: var(--tech-text);
    margin: 5px auto;
    transition: all 0.3s ease;
  }

  /* 装饰元素 */
  .tech-nav-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
  }

  .tech-grid-bg {
    position: absolute;
    top: 0;
    left: -50%;
    width: 200%;
    height: 100%;
    background-image:
      repeating-linear-gradient(90deg, transparent, transparent 50px, rgba(66, 165, 245, 0.03) 50px, rgba(66, 165, 245, 0.03) 51px),
      repeating-linear-gradient(0deg, transparent, transparent 50px, rgba(66, 165, 245, 0.03) 50px, rgba(66, 165, 245, 0.03) 51px);
    animation: gridMove 20s linear infinite;
  }

  /* 动画 */
  @keyframes navFadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  @keyframes gridMove {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(50px);
    }
  }

  /* 搜索容器样式 */
  .tech-search-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(18, 18, 35, 0.95);
    backdrop-filter: blur(var(--tech-glass-blur));
    -webkit-backdrop-filter: blur(var(--tech-glass-blur));
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 120px;
  }

  .tech-search-container.is-active {
    opacity: 1;
    visibility: visible;
  }

  .tech-search-container predictive-search {
    width: 100%;
    max-width: 800px;
    margin: 0 20px;
  }

  .tech-search-container .tech-search-content {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
  }

  .tech-search-container .search__input-wrap {
    position: relative !important;
    margin-bottom: 20px;
    display: block !important;
    width: 100% !important;
  }

  .tech-search-container .search__input {
    width: 100%;
    padding: 20px 60px 20px 50px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid var(--tech-border);
    border-radius: 12px;
    color: var(--tech-text);
    font-size: 18px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  /* 隐藏默认的搜索按钮，使用自定义图标 */
  .tech-search-container .btn--search {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 0;
    width: 20px;
    height: 20px;
    cursor: pointer;
    z-index: 1;
  }

  .tech-search-container .btn--search svg {
    width: 20px;
    height: 20px;
    stroke: rgba(255, 255, 255, 0.7);
    fill: none;
  }

  .tech-search-container .btn--search:hover svg {
    stroke: var(--tech-primary);
  }

  .tech-search-container .btn--search .icon__fallback-text {
    display: none;
  }

  .tech-search-container .search__input:focus {
    outline: none;
    border-color: var(--tech-primary);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 20px rgba({{ section.settings.primary_color | color_to_rgb }}, 0.3);
  }

  .tech-search-container .search__input::placeholder {
    color: var(--tech-text-muted);
  }

  /* 设置form为定位容器 */
  .tech-search-container predictive-search form {
    position: relative !important;
  }

  /* 重新定位关闭按钮 - 相对于搜索框定位 */
  .tech-search-container predictive-search form .btn--close-search {
    position: absolute !important;
    right: 32px !important;
    top: 5px !important;
    bottom: 20px !important;
    margin: auto 0 !important;
    background: none !important;
    border: none !important;
    color: var(--tech-text-muted) !important;
    cursor: pointer !important;
    padding: 6px !important;
    transition: all 0.3s ease !important;
    z-index: 2 !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    left: auto !important;
    transform: none !important;
  }

  .tech-search-container .btn--close-search:hover {
    color: var(--tech-primary);
    background: rgba(66, 165, 245, 0.1);
    transform: translateY(-50%) scale(1.1);
  }

  .tech-search-container .btn--close-search svg {
    width: 16px;
    height: 16px;
  }

  .tech-search-container .search__results {
    background: rgba(26, 26, 46, 0.98);
    border: 1px solid var(--tech-border);
    border-radius: 12px;
    max-height: 60vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .tech-search-container .predictive-search-results {
    padding: 0;
  }

  .tech-search-container .predictive-search-results--none {
    padding: 40px 20px;
    text-align: center;
    color: var(--tech-text-muted);
  }

  /* 搜索结果分组样式 */
  .tech-search-container .results__group-1,
  .tech-search-container .results__group-2 {
    border-bottom: 1px solid var(--tech-border);
  }

  .tech-search-container .results__group-1:last-child,
  .tech-search-container .results__group-2:last-child {
    border-bottom: none;
  }

  .tech-search-container .results {
    padding: 20px;
  }

  .tech-search-container .results:not(:last-child) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }

  .tech-search-container .results h3 {
    color: var(--tech-primary) !important;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .tech-search-container .results h3::before {
    content: '';
    width: 3px;
    height: 16px;
    background: var(--tech-gradient);
    border-radius: 2px;
  }

  .tech-search-container .results ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .tech-search-container .results li {
    margin-bottom: 4px;
  }

  .tech-search-container .results a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: rgba(255, 255, 255, 0.85);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
  }

  .tech-search-container .results a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: var(--tech-gradient);
    opacity: 0.1;
    transition: width 0.3s ease;
  }

  .tech-search-container .results a:hover {
    background: rgba(66, 165, 245, 0.08);
    color: var(--tech-text);
    transform: translateX(8px);
    box-shadow: 0 4px 15px rgba(66, 165, 245, 0.15);
  }

  .tech-search-container .results a:hover::before {
    width: 4px;
  }

  /* 产品搜索结果特殊样式 */
  .tech-search-container .results--products a {
    padding: 16px;
  }

  .tech-search-container .results-products__image {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    overflow: hidden;
    margin-right: 16px;
    background: rgba(255, 255, 255, 0.05);
    flex-shrink: 0;
    position: relative;
  }

  .tech-search-container .results-products__image::after {
    content: '';
    position: absolute;
    inset: 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    pointer-events: none;
  }

  .tech-search-container .results-products__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .tech-search-container .results--products a:hover .results-products__image img {
    transform: scale(1.05);
  }

  .tech-search-container .results-products__info {
    flex: 1;
    min-width: 0;
  }

  .tech-search-container .results-products__info > span {
    display: block;
    color: var(--tech-text);
    font-weight: 600;
    font-size: 15px;
    line-height: 1.4;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    max-height: 2.8em;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
  }

  .tech-search-container .grid-product__vendor {
    color: var(--tech-text-muted);
    font-size: 13px;
    font-weight: 500;
    opacity: 0.8;
  }

  /* 搜索建议样式 */
  .tech-search-container .results--queries a {
    font-weight: 500;
  }

  .tech-search-container .results--queries a::after {
    content: '→';
    margin-left: auto;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
  }

  .tech-search-container .results--queries a:hover::after {
    opacity: 1;
    transform: translateX(0);
  }

  /* 修复搜索建议中高亮文字的颜色 */
  .tech-search-container .results--queries a span,
  .tech-search-container .results--queries a mark,
  .tech-search-container .results--queries a strong,
  .tech-search-container .results--queries a em {
    color: var(--tech-primary) !important;
    background: rgba(66, 165, 245, 0.15) !important;
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 600;
  }

  /* 确保搜索建议标题保持正确颜色 */
  .tech-search-container .results--queries h3 {
    color: var(--tech-primary) !important;
  }

  /* 确保搜索建议链接文字有合适的颜色 */
  .tech-search-container .results--queries a > * {
    color: rgba(255, 255, 255, 0.85) !important;
  }

  /* 特别处理 styled_text 中的高亮部分 */
  .tech-search-container .results--queries [data-styled-text] mark,
  .tech-search-container .results--queries [data-styled-text] strong,
  .tech-search-container .results--queries [data-styled-text] span[style*="color"] {
    color: var(--tech-primary) !important;
    background: rgba(66, 165, 245, 0.15) !important;
  }

  /* 强制覆盖所有内联样式的黑色文字 */
  .tech-search-container .results--queries a span[style*="color: #000"],
  .tech-search-container .results--queries a span[style*="color: black"],
  .tech-search-container .results--queries a span[style*="color:#000"],
  .tech-search-container .results--queries a span[style*="color:black"],
  .tech-search-container .results--queries a *[style*="color: #000"],
  .tech-search-container .results--queries a *[style*="color: black"],
  .tech-search-container .results--queries a *[style*="color:#000"],
  .tech-search-container .results--queries a *[style*="color:black"] {
    color: var(--tech-primary) !important;
    background: rgba(66, 165, 245, 0.15) !important;
    padding: 2px 4px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
  }

  /* 通用的高亮文字样式覆盖 */
  .tech-search-container .predictive-search-results mark,
  .tech-search-container .predictive-search-results strong,
  .tech-search-container .predictive-search-results b,
  .tech-search-container .predictive-search-results em,
  .tech-search-container .predictive-search-results i {
    color: var(--tech-primary) !important;
    background: rgba(66, 165, 245, 0.15) !important;
    padding: 2px 4px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
  }

  /* 强力修复所有搜索结果文字颜色 - 覆盖所有可能的黑色文字 */
  .tech-search-container .predictive-search-results *,
  .tech-search-container .search__results *,
  .tech-search-container predictive-search *,
  .tech-search-container predictive-search a,
  .tech-search-container predictive-search span,
  .tech-search-container predictive-search div,
  .tech-search-container predictive-search p {
    color: rgba(255, 255, 255, 0.85) !important;
  }

  /* 特别针对"Show all results for..."链接 */
  .tech-search-container .predictive-search-results a[href*="search"],
  .tech-search-container .search__results a[href*="search"],
  .tech-search-container predictive-search a[href*="search"],
  .tech-search-container .predictive-search-results .results__footer,
  .tech-search-container .predictive-search-results .results__footer *,
  .tech-search-container .search__results .results__footer,
  .tech-search-container .search__results .results__footer *,
  .tech-search-container predictive-search form a,
  .tech-search-container predictive-search form span,
  .tech-search-container predictive-search form div {
    color: rgba(255, 255, 255, 0.85) !important;
    text-decoration: none !important;
  }

  /* 悬停状态和交互效果 */
  .tech-search-container .predictive-search-results a:hover,
  .tech-search-container .search__results a:hover,
  .tech-search-container predictive-search a:hover {
    color: var(--tech-primary) !important;
    background: rgba(66, 165, 245, 0.08) !important;
    transform: translateX(3px) !important;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1) !important;
  }

  /* 特别优化"Show all results for..."的交互效果 */
  .tech-search-container .predictive-search-results a[href*="search"],
  .tech-search-container .search__results a[href*="search"],
  .tech-search-container predictive-search a[href*="search"] {
    display: block !important;
    padding: 12px 16px !important;
    margin: 8px 0 !important;
    border-radius: 8px !important;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1) !important;
    position: relative !important;
    overflow: hidden !important;
  }

  .tech-search-container .predictive-search-results a[href*="search"]:hover,
  .tech-search-container .search__results a[href*="search"]:hover,
  .tech-search-container predictive-search a[href*="search"]:hover {
    background: rgba(66, 165, 245, 0.12) !important;
    color: var(--tech-text) !important;
    transform: translateX(5px) !important;
    box-shadow: 0 4px 15px rgba(66, 165, 245, 0.15) !important;
  }

  /* 添加左侧渐变边框效果 */
  .tech-search-container .predictive-search-results a[href*="search"]::before,
  .tech-search-container .search__results a[href*="search"]::before,
  .tech-search-container predictive-search a[href*="search"]::before {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 0 !important;
    height: 100% !important;
    background: var(--tech-gradient) !important;
    transition: width 0.3s ease !important;
  }

  .tech-search-container .predictive-search-results a[href*="search"]:hover::before,
  .tech-search-container .search__results a[href*="search"]:hover::before,
  .tech-search-container predictive-search a[href*="search"]:hover::before {
    width: 4px !important;
  }

  /* 强制覆盖任何内联样式的黑色文字 */
  .tech-search-container predictive-search *[style*="color: #000"],
  .tech-search-container predictive-search *[style*="color: black"],
  .tech-search-container predictive-search *[style*="color:#000"],
  .tech-search-container predictive-search *[style*="color:black"] {
    color: rgba(255, 255, 255, 0.85) !important;
  }

  /* 精确针对"Show all results for..."的交互效果 */
  .tech-search-container predictive-search > a:last-child,
  .tech-search-container .predictive-search-results > a:last-child,
  .tech-search-container .search__results > a:last-child,
  .tech-search-container predictive-search form > a:last-child {
    display: block !important;
    padding: 12px 16px !important;
    margin: 8px 0 !important;
    border-radius: 8px !important;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1) !important;
    position: relative !important;
    overflow: hidden !important;
  }

  .tech-search-container predictive-search > a:last-child:hover,
  .tech-search-container .predictive-search-results > a:last-child:hover,
  .tech-search-container .search__results > a:last-child:hover,
  .tech-search-container predictive-search form > a:last-child:hover {
    background: rgba(66, 165, 245, 0.12) !important;
    color: var(--tech-text) !important;
    transform: translateX(5px) !important;
    box-shadow: 0 4px 15px rgba(66, 165, 245, 0.15) !important;
  }

  /* 左侧渐变边框效果 - 只针对最后一个链接 */
  .tech-search-container predictive-search > a:last-child::before,
  .tech-search-container .predictive-search-results > a:last-child::before,
  .tech-search-container .search__results > a:last-child::before,
  .tech-search-container predictive-search form > a:last-child::before {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 0 !important;
    height: 100% !important;
    background: var(--tech-gradient) !important;
    transition: width 0.3s ease !important;
  }

  .tech-search-container predictive-search > a:last-child:hover::before,
  .tech-search-container .predictive-search-results > a:last-child:hover::before,
  .tech-search-container .search__results > a:last-child:hover::before,
  .tech-search-container predictive-search form > a:last-child:hover::before {
    width: 4px !important;
  }

  /* 页面和文章结果样式 */
  .tech-search-container .results--pages a,
  .tech-search-container .results--articles a {
    position: relative;
  }

  .tech-search-container .results--pages a::after,
  .tech-search-container .results--articles a::after {
    content: '';
    width: 6px;
    height: 6px;
    background: var(--tech-primary);
    border-radius: 50%;
    margin-left: auto;
    opacity: 0;
    transition: all 0.3s ease;
  }

  .tech-search-container .results--pages a:hover::after,
  .tech-search-container .results--articles a:hover::after {
    opacity: 1;
  }

  /* 热门搜索建议样式 */
  .tech-search-suggestions {
    margin-top: 20px;
    opacity: 1;
    transition: opacity 0.3s ease;
    width: 100%;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .tech-search-suggestions.hidden {
    opacity: 0;
    pointer-events: none;
  }

  .tech-suggestions-content {
    background: rgba(26, 26, 46, 0.98);
    border: 1px solid var(--tech-border);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .tech-suggestions-content h3 {
    color: var(--tech-primary);
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .tech-suggestions-content h3::before {
    content: '🔥';
    font-size: 14px;
  }

  .tech-suggestions-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .tech-suggestion-tag {
    background: rgba(66, 165, 245, 0.1);
    border: 1px solid rgba(66, 165, 245, 0.2);
    color: var(--tech-text-muted);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .tech-suggestion-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--tech-gradient);
    opacity: 0.1;
    transition: left 0.3s ease;
  }

  .tech-suggestion-tag:hover {
    background: rgba(66, 165, 245, 0.15);
    border-color: var(--tech-primary);
    color: var(--tech-text);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(66, 165, 245, 0.2);
  }

  .tech-suggestion-tag:hover::before {
    left: 0;
  }

  /* 搜索状态样式 */
  .tech-search-status {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
  }

  .tech-search-status.active {
    opacity: 1;
    visibility: visible;
  }

  .tech-search-loading {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(26, 26, 46, 0.95);
    padding: 16px 24px;
    border-radius: 12px;
    border: 1px solid var(--tech-border);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--tech-text-muted);
    font-size: 14px;
    font-weight: 500;
  }

  .tech-loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(66, 165, 245, 0.2);
    border-top: 2px solid var(--tech-primary);
    border-radius: 50%;
    animation: techSpin 1s linear infinite;
  }

  @keyframes techSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .tech-navbar-inner {
      height: var(--tech-navbar-mobile-height);
    }

    .tech-nav-menu {
      position: fixed;
      top: 60px;
      left: 0;
      right: 0;
      background: rgba(26, 26, 46, 0.98);
      flex-direction: column;
      padding: 40px 20px;
      gap: 20px;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      overflow-y: auto; /* 允许滚动 */
      z-index: 9999; /* 确保在最顶层 */
      min-height: calc(100vh - 60px); /* 最小高度为屏幕高度减去导航栏高度 */
      max-height: none; /* 移除最大高度限制 */
      height: auto; /* 自动高度适应内容 */
    }

    .tech-nav-menu.active {
      transform: translateX(0);
    }

    .tech-mobile-toggle {
      display: block;
    }

    .tech-nav-actions {
      display: none;
    }

    .tech-dropdown {
      position: static;
      transform: none;
      margin: 10px 0;
      opacity: 1;
      visibility: visible;
      background: rgba(0, 0, 0, 0.5); /* 增强背景透明度 */
      border-radius: 8px; /* 添加圆角 */
      padding: 15px; /* 增加内边距 */
      width: 100%; /* 确保全宽 */
      box-sizing: border-box; /* 包含padding在宽度内 */
    }

    .tech-search-container {
      padding-top: 80px;
    }

    .tech-search-container predictive-search {
      margin: 0 10px;
      max-width: calc(100vw - 20px);
    }

    .tech-search-suggestions {
      padding: 0 10px;
      max-width: calc(100vw - 20px);
    }

    .tech-search-container .search__input {
      padding: 16px 50px 16px 45px;
      font-size: 16px;
    }

    .tech-search-container .btn--search {
      left: 15px;
      width: 18px;
      height: 18px;
    }

    .tech-search-container .btn--search svg {
      width: 18px;
      height: 18px;
    }
  }
</style>

<nav class="tech-navbar" id="techNavbar">
  <div class="tech-nav-decoration">
    <div class="tech-grid-bg"></div>
  </div>

  <div class="tech-navbar-inner">
    <!-- Logo -->
    <a href="{{ routes.root_url }}" class="tech-logo">
      <img
        src="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/LOGO-NEW.png?v=1756444758"
        alt="{{ shop.name }}"
        class="tech-logo-image"
        style="max-width: 140px; height: auto;"
        width="140"
        height="60"
      >
    </a>

    <!-- 主导航 -->
    <ul class="tech-nav-menu" id="techNavMenu">
      {% for link in linklists[section.settings.menu].links %}
        <li class="tech-nav-item">
          {% comment %}
          为特定菜单项添加自定义子菜单
          {% endcomment %}

          {% if link.title == '3D Printer' or link.title == '3D Printing' %}
            <!-- 3D Printer 菜单项 -->
            <span class="tech-nav-link has-dropdown">
              {{ link.title }}
              <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                <path d="M3 5L6 8L9 5" stroke="currentColor" stroke-width="1.5" fill="none"/>
              </svg>
            </span>
            <div class="tech-dropdown">
              {% if section.settings.printer_submenu_1_text != blank %}
                <a href="/pages/ir3-v2-show" class="tech-dropdown-item">
                  {{ section.settings.printer_submenu_1_text }}
                </a>
              {% endif %}
              {% if section.settings.printer_submenu_2_text != blank %}
                <a href="/none" class="tech-dropdown-item">
                  {{ section.settings.printer_submenu_2_text }}
                </a>
              {% endif %}
            </div>

          {% elsif link.title == 'Accessories' %}
            <!-- Accessories 菜单项 -->
            <span class="tech-nav-link has-dropdown">
              {{ link.title }}
              <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                <path d="M3 5L6 8L9 5" stroke="currentColor" stroke-width="1.5" fill="none"/>
              </svg>
            </span>
            <div class="tech-dropdown">
              {% if section.settings.accessories_submenu_1_text != blank %}
                <a href="/collections/ir3-v2-printer-accessories" class="tech-dropdown-item">
                  {{ section.settings.accessories_submenu_1_text }}
                </a>
              {% endif %}
              {% if section.settings.accessories_submenu_2_text != blank %}
                <a href="/none" class="tech-dropdown-item">
                  {{ section.settings.accessories_submenu_2_text }}
                </a>
              {% endif %}
              {% if section.settings.accessories_submenu_3_text != blank %}
                <a href="/collections/all" class="tech-dropdown-item">
                  {{ section.settings.accessories_submenu_3_text }}
                </a>
              {% endif %}
            </div>

          {% else %}
            <!-- 其他菜单项保持原有逻辑 -->
            <a href="{{ link.url }}" class="tech-nav-link">
              {{ link.title }}
              {% if link.links.size > 0 %}
                <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                  <path d="M3 5L6 8L9 5" stroke="currentColor" stroke-width="1.5" fill="none"/>
                </svg>
              {% endif %}
            </a>

            {% if link.links.size > 0 %}
              <div class="tech-dropdown">
                {% for child_link in link.links %}
                  <a href="{{ child_link.url }}" class="tech-dropdown-item">
                    {{ child_link.title }}
                  </a>
                {% endfor %}
              </div>
            {% endif %}
          {% endif %}
        </li>
      {% endfor %}
    </ul>

    <!-- 右侧操作 -->
    <div class="tech-nav-actions">
      <!-- 搜索 -->
      <button class="tech-action-btn js-search-header">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <circle cx="9" cy="9" r="6" stroke="currentColor" stroke-width="1.5"/>
          <path d="M13 13L17 17" stroke="currentColor" stroke-width="1.5"/>
        </svg>
      </button>

      <!-- 购物车 -->
      <a href="{{ routes.cart_url }}" class="tech-action-btn">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M3 3H17L15 11H5L3 3Z" stroke="currentColor" stroke-width="1.5"/>
          <circle cx="7" cy="17" r="1" fill="currentColor"/>
          <circle cx="13" cy="17" r="1" fill="currentColor"/>
        </svg>
        {% if cart.item_count > 0 %}
          <span style="position: absolute; top: -5px; right: -5px; background: var(--tech-gradient); color: white; font-size: 11px; padding: 2px 6px; border-radius: 10px;">
            {{ cart.item_count }}
          </span>
        {% endif %}
      </a>

      <!-- CTA按钮 -->
      {% if section.settings.show_cta %}
        <a href="{{ section.settings.cta_link }}" class="tech-cta-btn">
          {{ section.settings.cta_text }}
        </a>
      {% endif %}
    </div>

    <!-- 移动端菜单按钮 -->
    <button class="tech-mobile-toggle" onclick="toggleMobileMenu()">
      <span></span>
      <span></span>
      <span></span>
    </button>
  </div>
</nav>

<!-- 搜索容器 -->
<div class="tech-search-container site-header__search-container">
  <div class="tech-search-content">
    {% render 'predictive-search', context: 'header' %}

    <!-- 热门搜索建议 -->
    <div class="tech-search-suggestions" id="techSearchSuggestions">
      <div class="tech-suggestions-content">
        <h3>热门搜索</h3>
        <div class="tech-suggestions-tags">
          {% if section.settings.popular_search_1 != blank %}
            <button class="tech-suggestion-tag" data-search="{{ section.settings.popular_search_1 }}">
              {{ section.settings.popular_search_1 }}
            </button>
          {% endif %}
          {% if section.settings.popular_search_2 != blank %}
            <button class="tech-suggestion-tag" data-search="{{ section.settings.popular_search_2 }}">
              {{ section.settings.popular_search_2 }}
            </button>
          {% endif %}
          {% if section.settings.popular_search_3 != blank %}
            <button class="tech-suggestion-tag" data-search="{{ section.settings.popular_search_3 }}">
              {{ section.settings.popular_search_3 }}
            </button>
          {% endif %}
          {% if section.settings.popular_search_4 != blank %}
            <button class="tech-suggestion-tag" data-search="{{ section.settings.popular_search_4 }}">
              {{ section.settings.popular_search_4 }}
            </button>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- 搜索状态提示 -->
    <div class="tech-search-status" id="techSearchStatus">
      <div class="tech-search-loading">
        <div class="tech-loading-spinner"></div>
        <span>搜索中...</span>
      </div>
    </div>
  </div>
</div>

<script>
  // 滚动效果
  window.addEventListener('scroll', function() {
    const navbar = document.getElementById('techNavbar');
    if (window.scrollY > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });

  // 初始化导航栏，处理内容偏移
  document.addEventListener('DOMContentLoaded', function() {
    // 等待DOM完全加载
    setTimeout(function() {
      const navbar = document.getElementById('techNavbar');
      if (navbar) {
        // 获取导航栏高度
        const navbarHeight = navbar.offsetHeight;

        // 添加初始化标记
        document.body.classList.add('tech-navbar-initialized');

        // 处理特殊组件的顶部边距，以确保视觉内容不被导航栏遮挡
        const fullScreenSections = document.querySelectorAll('.custom-video-banner--100vh, [style*="height: 100vh"]');
        fullScreenSections.forEach(function(section) {
          // 对于全屏幕高度的组件，增加顶部内边距
          if (window.innerWidth > 768) {
            section.style.paddingTop = navbarHeight + 'px';
          } else {
            const mobileNavHeight = navbar.querySelector('.tech-navbar-inner').offsetHeight;
            section.style.paddingTop = mobileNavHeight + 'px';
          }
        });
      }
    }, 100);
  });

  // 移动端菜单切换
  function toggleMobileMenu() {
    const menu = document.getElementById('techNavMenu');
    menu.classList.toggle('active');
  }

  // 搜索功能
  function initTechSearch() {
    const searchBtn = document.querySelector('.js-search-header');
    const searchContainer = document.querySelector('.tech-search-container');
    const searchInput = searchContainer ? searchContainer.querySelector('.search__input') : null;
    const searchSuggestions = document.getElementById('techSearchSuggestions');
    const searchStatus = document.getElementById('techSearchStatus');
    const suggestionTags = document.querySelectorAll('.tech-suggestion-tag');

    if (searchBtn && searchContainer) {
      searchBtn.addEventListener('click', function(evt) {
        evt.preventDefault();
        evt.stopPropagation();

        // 触发预测搜索打开事件
        document.dispatchEvent(new CustomEvent('predictive-search:open', {
          detail: {
            context: 'header'
          },
          bubbles: true
        }));

        // 显示搜索容器
        searchContainer.classList.add('is-active');

        // 显示热门搜索建议
        if (searchSuggestions) {
          searchSuggestions.classList.remove('hidden');
        }
      });
    }

    // 热门搜索标签点击事件
    suggestionTags.forEach(tag => {
      tag.addEventListener('click', function() {
        const searchTerm = this.getAttribute('data-search');
        if (searchInput && searchTerm) {
          searchInput.value = searchTerm;
          searchInput.focus();

          // 隐藏建议，显示加载状态
          if (searchSuggestions) {
            searchSuggestions.classList.add('hidden');
          }
          showSearchStatus();

          // 触发搜索
          setTimeout(() => {
            searchInput.dispatchEvent(new Event('input', { bubbles: true }));
          }, 100);
        }
      });
    });

    // 搜索输入事件监听
    if (searchInput) {
      searchInput.addEventListener('input', function() {
        const hasValue = this.value.trim().length > 0;

        if (hasValue) {
          // 隐藏建议
          if (searchSuggestions) {
            searchSuggestions.classList.add('hidden');
          }
          showSearchStatus();
        } else {
          // 显示建议
          if (searchSuggestions) {
            searchSuggestions.classList.remove('hidden');
          }
          hideSearchStatus();
        }
      });

      searchInput.addEventListener('focus', function() {
        if (!this.value.trim()) {
          if (searchSuggestions) {
            searchSuggestions.classList.remove('hidden');
          }
        }
      });
    }

    // 显示搜索状态
    function showSearchStatus() {
      if (searchStatus) {
        searchStatus.classList.add('active');
        // 模拟搜索延迟后隐藏状态
        setTimeout(() => {
          hideSearchStatus();
        }, 1000);
      }
    }

    // 隐藏搜索状态
    function hideSearchStatus() {
      if (searchStatus) {
        searchStatus.classList.remove('active');
      }
    }

    // 监听搜索关闭事件
    document.addEventListener('predictive-search:close-all', function() {
      if (searchContainer) {
        searchContainer.classList.remove('is-active');
      }
      if (searchSuggestions) {
        searchSuggestions.classList.remove('hidden');
      }
      hideSearchStatus();
    });

    // ESC键关闭搜索
    document.addEventListener('keydown', function(e) {
      if (e.keyCode === 27 && searchContainer && searchContainer.classList.contains('is-active')) {
        document.dispatchEvent(new CustomEvent('predictive-search:close'));
      }
    });

    // 点击搜索容器背景关闭搜索
    if (searchContainer) {
      searchContainer.addEventListener('click', function(e) {
        if (e.target === searchContainer) {
          document.dispatchEvent(new CustomEvent('predictive-search:close'));
        }
      });
    }
  }

  // 磁性按钮效果
  document.querySelectorAll('.tech-action-btn, .tech-cta-btn').forEach(btn => {
    btn.addEventListener('mousemove', function(e) {
      const rect = btn.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;

      btn.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px) scale(1.05)`;
    });

    btn.addEventListener('mouseleave', function() {
      btn.style.transform = '';
    });
  });

  // 初始化搜索功能
  initTechSearch();
</script>

{% schema %}
{
  "name": "科技风格导航栏",
  "settings": [
    {
      "type": "header",
      "content": "Logo设置"
    },
    {
      "type": "image_picker",
      "id": "logo_image",
      "label": "Logo图片"
    },
    {
      "type": "range",
      "id": "logo_width",
      "min": 50,
      "max": 300,
      "step": 10,
      "default": 140,
      "unit": "px",
      "label": "Logo宽度"
    },
    {
      "type": "header",
      "content": "导航菜单"
    },
    {
      "type": "link_list",
      "id": "menu",
      "label": "主菜单",
      "default": "main-menu"
    },
    {
      "type": "header",
      "content": "3D Printer 子菜单"
    },
    {
      "type": "text",
      "id": "printer_submenu_1_text",
      "label": "子菜单项 1 文字",
      "default": "IR3 V2"
    },
    {
      "type": "url",
      "id": "printer_submenu_1_url",
      "label": "子菜单项 1 链接"
    },
    {
      "type": "header",
      "content": "Accessories 子菜单"
    },
    {
      "type": "text",
      "id": "accessories_submenu_1_text",
      "label": "子菜单项 1 文字",
      "default": "IR3 V2 Accessories"
    },
    {
      "type": "url",
      "id": "accessories_submenu_1_url",
      "label": "子菜单项 1 链接"
    },
    {
      "type": "text",
      "id": "accessories_submenu_2_text",
      "label": "子菜单项 2 文字",
      "default": "IR3 V1 Accessories"
    },
    {
      "type": "url",
      "id": "accessories_submenu_2_url",
      "label": "子菜单项 2 链接"
    },
    {
      "type": "text",
      "id": "accessories_submenu_3_text",
      "label": "子菜单项 3 文字",
      "default": "Other Accessories"
    },
    {
      "type": "url",
      "id": "accessories_submenu_3_url",
      "label": "子菜单项 3 链接"
    },
    {
      "type": "header",
      "content": "CTA按钮"
    },
    {
      "type": "checkbox",
      "id": "show_cta",
      "label": "显示CTA按钮",
      "default": true
    },
    {
      "type": "text",
      "id": "cta_text",
      "label": "CTA文字",
      "default": "立即体验"
    },
    {
      "type": "url",
      "id": "cta_link",
      "label": "CTA链接"
    },
    {
      "type": "header",
      "content": "样式设置"
    },
    {
      "type": "color",
      "id": "primary_color",
      "label": "主色调",
      "default": "#42a5f5"
    },
    {
      "type": "color",
      "id": "secondary_color",
      "label": "辅助色",
      "default": "#1de9b6"
    },
    {
      "type": "checkbox",
      "id": "enable_animations",
      "label": "启用动画效果",
      "default": true
    }
,
    {
      "type": "header",
      "content": "搜索设置"
    },
    {
      "type": "text",
      "id": "popular_search_1",
      "label": "热门搜索 1",
      "default": "3D Printer"
    },
    {
      "type": "text",
      "id": "popular_search_2",
      "label": "热门搜索 2",
      "default": "IR3 V2"
    },
    {
      "type": "text",
      "id": "popular_search_3",
      "label": "热门搜索 3",
      "default": "Accessories"
    },
    {
      "type": "text",
      "id": "popular_search_4",
      "label": "热门搜索 4",
      "default": "Build Plate"
    }
  ],
  "presets": [
    {
      "name": "科技风格导航栏",
      "category": "导航"
    }
  ]
}
{% endschema %}
